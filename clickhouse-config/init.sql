-- ClickHouse 初始化脚本
-- 创建示例数据库
CREATE DATABASE IF NOT EXISTS test_db;

-- 创建示例表
CREATE TABLE IF NOT EXISTS test_db.events (
    id UInt64,
    timestamp DateTime,
    user_id UInt32,
    event_type String,
    data String
) ENGINE = MergeTree()
ORDER BY (timestamp, id);

-- 插入示例数据
INSERT INTO test_db.events VALUES 
(1, '2024-01-01 10:00:00', 1001, 'login', '{"ip": "***********"}'),
(2, '2024-01-01 10:05:00', 1001, 'click', '{"page": "home"}'),
(3, '2024-01-01 10:10:00', 1002, 'login', '{"ip": "***********"}');
