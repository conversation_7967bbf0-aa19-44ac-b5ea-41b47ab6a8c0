#!/bin/bash

echo "=== ClickHouse Docker 测试脚本 ==="

# 检查 ClickHouse 容器是否运行
echo "检查 ClickHouse 容器状态..."
if docker ps | grep -q clickhouse_db; then
    echo "✅ ClickHouse 容器正在运行"
else
    echo "❌ ClickHouse 容器未运行，请先启动服务"
    echo "运行: docker-compose up -d clickhouse"
    exit 1
fi

# 等待 ClickHouse 启动
echo "等待 ClickHouse 服务启动..."
sleep 5

# 测试 HTTP 接口连接
echo "测试 HTTP 接口 (端口 8123)..."
if curl -s http://localhost:8123/ > /dev/null; then
    echo "✅ HTTP 接口连接成功"
else
    echo "❌ HTTP 接口连接失败"
fi

# 测试基本查询
echo "测试基本查询..."
echo "SELECT version()" | curl -s 'http://localhost:8123/' --data-binary @-

# 执行初始化脚本
echo "执行初始化脚本..."
cat clickhouse-config/init.sql | curl -s 'http://localhost:8123/' --data-binary @-

# 测试查询示例数据
echo "查询示例数据..."
echo "SELECT * FROM test_db.events" | curl -s 'http://localhost:8123/' --data-binary @-

echo "=== 测试完成 ==="
echo "ClickHouse Web UI: http://localhost:8123/play"
echo "连接信息:"
echo "  HTTP 端口: 8123"
echo "  Native 端口: 9000"
echo "  用户名: default"
echo "  密码: (空)"
