#!/bin/bash

echo "🚀 启动 Docker 服务..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 启动服务
docker-compose up -d

echo "✅ 服务启动完成！"
echo ""
echo "📊 服务状态："
docker-compose ps
echo ""
echo "🔗 连接信息："
echo "PostgreSQL: localhost:5432"
echo "  - 数据库: myapp"
echo "  - 用户名: postgres"
echo "  - 密码: postgres123"
echo ""
echo "Redis: localhost:6379"
echo ""
echo "📝 常用命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart" 