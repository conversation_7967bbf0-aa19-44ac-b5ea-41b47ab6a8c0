# Docker 开发环境配置

本项目配置了 Redis 和 PostgreSQL 的 Docker 开发环境。

## 🚀 快速开始

### 1. 启动服务
```bash
chmod +x start.sh
./start.sh
```

### 2. 停止服务
```bash
chmod +x stop.sh
./stop.sh
```

## 📊 服务配置

### PostgreSQL
- **端口**: 5432
- **数据库**: myapp
- **用户名**: postgres
- **密码**: postgres123
- **版本**: 15

### Redis
- **端口**: 6379
- **版本**: 7-alpine
- **持久化**: 已启用 AOF

## 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 进入容器
docker exec -it postgres_db psql -U postgres -d myapp
docker exec -it redis_cache redis-cli

# 完全清理（删除数据）
docker-compose down -v
```

## 📁 文件结构

```
.
├── docker-compose.yml    # Docker Compose 配置
├── redis.conf           # Redis 配置文件
├── init-scripts/        # PostgreSQL 初始化脚本
│   └── 01-init.sql
├── start.sh            # 启动脚本
├── stop.sh             # 停止脚本
└── README.md           # 说明文档
```

## 🔒 安全注意事项

1. 生产环境中请修改默认密码
2. 考虑启用 Redis 密码认证
3. 根据需要调整网络配置

## 🛠️ 故障排除

### 端口冲突
如果端口被占用，可以修改 `docker-compose.yml` 中的端口映射。

### 权限问题
确保脚本有执行权限：
```bash
chmod +x *.sh
```

### 数据持久化
数据存储在 Docker 卷中，重启容器数据不会丢失。 