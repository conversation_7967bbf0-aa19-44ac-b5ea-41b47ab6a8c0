#!/bin/bash

echo "🧪 开始测试 Docker 服务..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_service() {
    local service_name=$1
    local test_command=$2
    local description=$3
    
    echo -e "${BLUE}🔍 测试 $service_name: $description${NC}"
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name 测试通过${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name 测试失败${NC}"
        return 1
    fi
}

# 检查服务是否运行
echo -e "${YELLOW}📊 检查服务状态...${NC}"
if ! docker-compose ps | grep -q "Up"; then
    echo -e "${RED}❌ 服务未运行，请先运行 ./start.sh${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 服务正在运行${NC}"
echo ""

# 测试 PostgreSQL
echo -e "${YELLOW}🐘 PostgreSQL 测试${NC}"
echo "----------------------------------"

# 测试连接
test_service "PostgreSQL连接" \
    "docker exec -it postgres_db psql -U postgres -d myapp -c 'SELECT 1;'" \
    "数据库连接"

# 测试表查询
test_service "PostgreSQL数据查询" \
    "docker exec -it postgres_db psql -U postgres -d myapp -c 'SELECT COUNT(*) FROM users;'" \
    "用户表查询"

# 测试插入数据
test_service "PostgreSQL数据插入" \
    "docker exec -it postgres_db psql -U postgres -d myapp -c \"INSERT INTO users (username, email) VALUES ('test_user', '<EMAIL>') ON CONFLICT (username) DO NOTHING;\"" \
    "数据插入"

# 测试数据完整性
test_service "PostgreSQL数据完整性" \
    "docker exec -it postgres_db psql -U postgres -d myapp -c 'SELECT username, email FROM users WHERE username = \"test_user\";'" \
    "数据完整性验证"

echo ""

# 测试 Redis
echo -e "${YELLOW}🔴 Redis 测试${NC}"
echo "----------------------------------"

# 测试连接
test_service "Redis连接" \
    "docker exec -it redis_cache redis-cli ping" \
    "Redis连接"

# 测试基本操作
test_service "Redis SET操作" \
    "docker exec -it redis_cache redis-cli SET test_key 'Hello Redis'" \
    "SET操作"

# 测试 GET 操作
test_service "Redis GET操作" \
    "docker exec -it redis_cache redis-cli GET test_key" \
    "GET操作"

# 测试列表操作
test_service "Redis列表操作" \
    "docker exec -it redis_cache redis-cli LPUSH test_list 'item1' 'item2' 'item3'" \
    "列表操作"

# 测试哈希操作
test_service "Redis哈希操作" \
    "docker exec -it redis_cache redis-cli HSET test_hash field1 'value1' field2 'value2'" \
    "哈希操作"

# 测试键过期
test_service "Redis过期设置" \
    "docker exec -it redis_cache redis-cli SETEX expire_key 10 'will expire'" \
    "过期设置"

echo ""

# 性能测试
echo -e "${YELLOW}⚡ 性能测试${NC}"
echo "----------------------------------"

# PostgreSQL 性能测试
echo -e "${BLUE}🔍 PostgreSQL 性能测试${NC}"
echo "创建测试表..."
docker exec -it postgres_db psql -U postgres -d myapp -c "
CREATE TABLE IF NOT EXISTS performance_test (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    value INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" > /dev/null 2>&1

echo "插入测试数据..."
docker exec -it postgres_db psql -U postgres -d myapp -c "
INSERT INTO performance_test (name, value) 
SELECT 'test_' || i, i 
FROM generate_series(1, 1000) i;" > /dev/null 2>&1

echo "执行查询测试..."
start_time=$(date +%s.%N)
docker exec -it postgres_db psql -U postgres -d myapp -c "SELECT COUNT(*) FROM performance_test WHERE value > 500;" > /dev/null 2>&1
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
echo -e "${GREEN}✅ PostgreSQL 查询耗时: ${duration}秒${NC}"

# Redis 性能测试
echo -e "${BLUE}🔍 Redis 性能测试${NC}"
echo "执行批量操作测试..."
start_time=$(date +%s.%N)
for i in {1..1000}; do
    docker exec -it redis_cache redis-cli SET "perf_key_$i" "value_$i" > /dev/null 2>&1
done
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
echo -e "${GREEN}✅ Redis 批量SET耗时: ${duration}秒${NC}"

echo ""

# 清理测试数据
echo -e "${YELLOW}🧹 清理测试数据${NC}"
echo "----------------------------------"
docker exec -it postgres_db psql -U postgres -d myapp -c "DELETE FROM users WHERE username = 'test_user';" > /dev/null 2>&1
docker exec -it postgres_db psql -U postgres -d myapp -c "DROP TABLE IF EXISTS performance_test;" > /dev/null 2>&1
docker exec -it redis_cache redis-cli DEL test_key test_list test_hash expire_key > /dev/null 2>&1
for i in {1..1000}; do
    docker exec -it redis_cache redis-cli DEL "perf_key_$i" > /dev/null 2>&1
done
echo -e "${GREEN}✅ 测试数据清理完成${NC}"

echo ""
echo -e "${GREEN}🎉 所有测试完成！${NC}"
echo "=================================="
echo -e "${BLUE}📝 测试总结：${NC}"
echo "- PostgreSQL: 连接、查询、插入、数据完整性测试"
echo "- Redis: 连接、基本操作、列表、哈希、过期设置测试"
echo "- 性能: 批量操作性能测试"
echo ""
echo -e "${YELLOW}💡 提示：${NC}"
echo "- 如需查看详细日志，请运行: docker-compose logs -f"
echo "- 如需进入容器调试，请运行: docker exec -it [容器名] [命令]" 